import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronUp, Users, CheckCircle, Zap, Shield, BookOpen, Globe, Star } from 'lucide-react';
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import Hero from "@/components/Hero";
import SuccessStories from "@/components/SuccessStories";
import BlogsSection from "@/components/BlogsSection";
import CaseStudiesSection from "@/components/CaseStudiesSection";
import TestimonialsSection from "@/components/TestimonialsSection";
import CtaSection from "@/components/CtaSection";
import Footer from "@/components/Footer";
import SEO from "@/components/SEO";
import { useSEO } from "@/hooks/useSEO";



const Index = () => {
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const { generateSEO } = useSEO();

  // Generate SEO for homepage
  const homeSEO = generateSEO.page('home');

  // Debug logging
  console.log('Index page - Generated SEO:', homeSEO);

  // Show button when page is scrolled down
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);

    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="flex flex-col min-h-screen">
      <SEO {...homeSEO} />
      <Header />
      <main className="flex-grow">
        <Hero />


        <SuccessStories />

        <BlogsSection />

        <CaseStudiesSection />
        {/* Features Section with hover effects */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-foreground">All the Features You Need to Learn, Connect, and Grow</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                From real founder journeys to exclusive resources and networking—discover everything we offer to power your startup journey
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-card p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2 border border-border">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 transition-colors duration-300 group-hover:bg-primary/20">
                  <BookOpen className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-foreground">Real Founder Stories</h3>
                <p className="text-muted-foreground">
                  Discover and learn from genuine stories shared by tech entrepreneurs who've built and grown startups from scratch.
                </p>
              </div>

              <div className="bg-card p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2 border border-border">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-foreground">In-Depth Case Studies</h3>
                <p className="text-muted-foreground">
                  Dive into premium case studies packed with real numbers, growth tactics, and lessons learned—unlock actionable insights for your own journey.
                </p>
              </div>

              <div className="bg-card p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2 border border-border">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-4">
                  <Star className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-foreground">Verified Entrepreneur Directory</h3>
                <p className="text-muted-foreground">
                  Explore a curated directory of verified founders and startups, complete with blue badges and detailed startup profiles.
                </p>
              </div>

              <div className="bg-card p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2 border border-border">
                <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center mb-4">
                  <Globe className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-foreground">Community & Comments</h3>
                <p className="text-muted-foreground">
                  Connect with like-minded founders—comment on stories, ask questions, share advice, and build your network.
                </p>
              </div>

              <div className="bg-card p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2 border border-border">
                <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-foreground">Weekly Newsletter</h3>
                <p className="text-muted-foreground">
                  Get the latest founder stories, case studies, and exclusive tips delivered straight to your inbox every week.
                </p>
              </div>

              <div className="bg-card p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2 border border-border">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-foreground">Startup Promotion Opportunities</h3>
                <p className="text-muted-foreground">
                  Feature your startup, get listed in our directory, or boost your visibility with sponsored listings and founder interviews.
                </p>
              </div>
            </div>
          </div>
        </section>

        <TestimonialsSection />
        {/* About Us Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-6 text-foreground">About Us</h2>
              <p className="text-lg text-muted-foreground mb-6">
              We believe every startup has a story worth sharing and that real lessons come from real journeys.
              </p>
              <p className="text-muted-foreground mb-6">
              Here, entrepreneurs from around the world share honest stories of how they built and grew their tech businesses: the highs, the lows, the pivots, and the breakthroughs.
              </p>
              <p className="text-muted-foreground mb-6">
              We bring together like-minded founders, investors, and builders to connect, support each other, and celebrate every win, big or small.
              </p>
              <p className="text-muted-foreground mb-6">
                Whether you're launching your first app, growing a SaaS business, or just curious about what it really takes to build something from scratch, you'll find inspiration, practical advice, and new friends right here.
              </p>
              <p className="text-muted-foreground mb-6">
              Welcome to a place where founder stories matter because yours might be next.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mt-12">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">By Entrepreneurs</h3>
                  <p className="text-muted-foreground">Created by entrepreneurs, for entrepreneurs</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">Practical Insights</h3>
                  <p className="text-muted-foreground">Focused on practical, actionable insights rather than theory</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">Diverse Perspectives</h3>
                  <p className="text-muted-foreground">Committed to diversity of thought, experience, and perspective</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <CtaSection />

      </main>

      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}

      <Footer />
    </div>
  );
};

export default Index;